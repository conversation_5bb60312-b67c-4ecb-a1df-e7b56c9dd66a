'use client';

import React, { useState } from 'react';
import { Modal } from '@/components/common';
import { AccountSettings } from './AccountSettings';
import { GeneralSettings } from './GeneralSettings';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { 
  User as UserIcon, 
  Settings as SettingsIcon
} from 'lucide-react';

type SettingsTab = 
  | 'general' 
  | 'account';

interface SettingsModalProps {
  show: boolean;
  onClose: () => void;
  selectedTab?: SettingsTab;
}

interface TabConfig {
  id: SettingsTab;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  component: React.ComponentType<any>;
}



export const SettingsModal: React.FC<SettingsModalProps> = ({
  show,
  onClose,
  selectedTab = 'general'
}) => {
  const [activeTab, setActiveTab] = useState<SettingsTab>(selectedTab);

  const tabs: TabConfig[] = [
    {
      id: 'general',
      label: 'General',
      icon: SettingsIcon,
      component: GeneralSettings
    },
    {
      id: 'account',
      label: 'Account',
      icon: UserIcon,
      component: AccountSettings
    }
  ];

  const activeTabConfig = tabs.find(tab => tab.id === activeTab);
  const ActiveComponent = activeTabConfig?.component;

  const handleSaveSettings = async (settings: any) => {
    // Implementation for saving settings following ifm-chat-feature-k2 pattern
    console.log('SettingsModal handleSaveSettings: Starting with settings:', settings);
    
    try {
      const { useSettingsStore, useAppStore, useAuthStore } = await import('@/lib/stores');
      const { updateUserSettings } = await import('@/lib/api/users');
      
      const settingsStore = useSettingsStore.getState();
      const appStore = useAppStore.getState();
      const { token, isAuthenticated } = useAuthStore.getState();
      
      console.log('SettingsModal: Auth state - token exists:', !!token, 'isAuthenticated:', isAuthenticated);
      
      if (!token) {
        throw new Error('No authentication token available');
      }
      
      // Update local settings store
      console.log('SettingsModal: Updating local settings store...');
      settingsStore.updateSettings(settings);
      console.log('SettingsModal: Local settings updated');
      
      // Refresh models (matching ifm-chat-feature-k2 behavior) - don't let this fail the save
      try {
        console.log('SettingsModal: Refreshing models...');
        await appStore.refreshModels(token);
        console.log('SettingsModal: Models refreshed successfully');
      } catch (error) {
        console.warn('SettingsModal: Failed to refresh models, but continuing with settings save:', error);
      }
      
      // Update user settings on server with ui wrapper
      console.log('SettingsModal: Updating user settings on server...');
      console.log('SettingsModal: Current settingsStore.settings:', settingsStore.settings);
      console.log('SettingsModal: Sending to API:', { ui: settingsStore.settings });
      
      await updateUserSettings(token, { ui: settingsStore.settings });
      console.log('SettingsModal: User settings updated successfully');
      
    } catch (error) {
      console.error('SettingsModal handleSaveSettings: Error occurred:', error);
      throw error; // Re-throw so GeneralSettings can handle it
    }
  };

  const handleSaveSuccess = () => {
    // Show success toast and close modal, matching ifm-chat-feature-k2 behavior
    toast.success('Settings saved successfully!');
    onClose();
  };

  return (
    <Modal
      show={show}
      onClose={onClose}
      size="lg"
      className="bg-white dark:bg-gray-900"
    >
      <div className="flex flex-col h-[80vh] max-h-[800px]">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Settings
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar */}
          <div className="w-64 border-r border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div className="p-4">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={cn(
                        "w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                        activeTab === tab.id
                          ? "bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200"
                          : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white"
                      )}
                    >
                      <Icon className="w-5 h-5 mr-3" />
                      {tab.label}
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-6">
              {ActiveComponent && (
                <ActiveComponent
                  saveSettings={handleSaveSettings}
                  onSave={handleSaveSuccess}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};
