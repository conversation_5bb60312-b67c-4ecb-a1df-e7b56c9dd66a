'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { AppLayout } from '@/components/layout/AppLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ChatItem } from '@/components/layout/sidebar/ChatItem';
import { useAuthStore } from '@/lib/stores';
import { cn } from '@/lib/utils';
import { 
  ArrowLeft, 
  Search,
  Archive,
  MessageSquare,
  Unarchive
} from 'lucide-react';
import {
  getChatList,
  getChatById,
  updateChatById,
  deleteChatById,
  cloneChatById,
  shareChatById,
  archiveChatById,
  toggleChatPinnedById,
  generateTitle
} from '@/lib/api';

interface ArchivedChat {
  id: string;
  title: string;
  updated_at: string;
  created_at: string;
  pinned?: boolean;
  archived: boolean;
  tags?: string[];
  folder_id?: string;
  chat?: {
    messages?: Record<string, unknown>[];
    models?: string[];
  };
}

export default function ArchivedChatsPage() {
  const router = useRouter();
  const { token } = useAuthStore();
  const [archivedChats, setArchivedChats] = useState<ArchivedChat[]>([]);
  const [filteredChats, setFilteredChats] = useState<ArchivedChat[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [shiftKey, setShiftKey] = useState(false);

  // Load archived chats
  const loadArchivedChats = useCallback(async () => {
    if (!token) return;
    
    setIsLoading(true);
    try {
      // In real implementation, this would be a separate API endpoint for archived chats
      // For now, we'll filter all chats to show only archived ones
      const allChats = await getChatList(token, 1);
      const archived = allChats?.filter((chat: ArchivedChat) => chat.archived) || [];
      setArchivedChats(archived);
      setFilteredChats(archived);
    } catch (error) {
      console.error('Failed to load archived chats:', error);
    } finally {
      setIsLoading(false);
    }
  }, [token]);

  useEffect(() => {
    loadArchivedChats();
  }, [loadArchivedChats]);

  // Filter chats based on search
  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = archivedChats.filter(chat =>
        chat.title?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredChats(filtered);
    } else {
      setFilteredChats(archivedChats);
    }
  }, [searchQuery, archivedChats]);

  // Keyboard event handling
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      setShiftKey(e.shiftKey);
    };
    
    const handleKeyUp = (e: KeyboardEvent) => {
      setShiftKey(e.shiftKey);
    };
    
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, []);

  // Chat handlers
  const handleChatRename = useCallback(async (chatId: string, newTitle: string) => {
    if (!token) return;
    
    try {
      const chat = await getChatById(token, chatId);
      if (chat) {
        const updatedChat = {
          ...chat.chat,
          title: newTitle
        };
        await updateChatById(token, chatId, updatedChat);
        await loadArchivedChats();
      }
    } catch (error) {
      console.error('Failed to rename chat:', error);
    }
  }, [token, loadArchivedChats]);
  
  const handleChatDelete = useCallback(async (chatId: string) => {
    if (!token) return;
    
    try {
      await deleteChatById(token, chatId);
      await loadArchivedChats();
    } catch (error) {
      console.error('Failed to delete chat:', error);
    }
  }, [token, loadArchivedChats]);
  
  const handleChatClone = useCallback(async (chatId: string) => {
    if (!token) return;
    
    try {
      const chat = archivedChats.find(c => c.id === chatId);
      if (chat) {
        const cloneTitle = `Clone of ${chat.title}`;
        await cloneChatById(token, chatId, cloneTitle);
        await loadArchivedChats();
      }
    } catch (error) {
      console.error('Failed to clone chat:', error);
    }
  }, [token, archivedChats, loadArchivedChats]);
  
  const handleChatShare = useCallback(async (chatId: string) => {
    if (!token) return;
    
    try {
      const sharedChat = await shareChatById(token, chatId);
      if (sharedChat) {
        console.log('Chat shared successfully:', sharedChat);
      }
    } catch (error) {
      console.error('Failed to share chat:', error);
    }
  }, [token]);
  
  const handleChatUnarchive = useCallback(async (chatId: string) => {
    if (!token) return;
    
    try {
      await archiveChatById(token, chatId); // This will toggle archive status
      await loadArchivedChats();
    } catch (error) {
      console.error('Failed to unarchive chat:', error);
    }
  }, [token, loadArchivedChats]);
  
  const handleChatPin = useCallback(async (chatId: string) => {
    if (!token) return;
    
    try {
      await toggleChatPinnedById(token, chatId);
      await loadArchivedChats();
    } catch (error) {
      console.error('Failed to toggle pin status:', error);
    }
  }, [token, loadArchivedChats]);
  
  const handleGenerateTitle = useCallback(async (chatId: string): Promise<string | null> => {
    if (!token) return null;
    
    try {
      const chat = await getChatById(token, chatId);
      if (chat && chat.chat?.messages) {
        const messages = Object.values(chat.chat.messages).map((msg: Record<string, unknown>) => ({
          role: msg.role,
          content: msg.content
        }));
        
        const model = chat.chat.models?.[0] || 'gpt-3.5-turbo';
        return await generateTitle(token, model, messages);
      }
      return null;
    } catch (error) {
      console.error('Failed to generate title:', error);
      return null;
    }
  }, [token]);
  
  const handleChatSelect = useCallback((chatId: string) => {
    setSelectedChat(chatId);
    router.push(`/c/${chatId}`);
  }, [router]);
  
  const handleChatUnselect = useCallback(() => {
    setSelectedChat(null);
  }, []);

  if (isLoading) {
    return (
      <AppLayout>
        <div className="h-full flex flex-col">
          <div className="flex items-center gap-4 p-6 border-b border-gray-200 dark:border-gray-800">
            <Button variant="ghost" size="icon" onClick={() => router.back()}>
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <h1 className="text-2xl font-semibold">Archived Chats</h1>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-gray-500 text-sm">Loading archived chats...</div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center gap-4 p-6 border-b border-gray-200 dark:border-gray-800">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <h1 className="text-2xl font-semibold flex items-center gap-2">
            <Archive className="w-6 h-6" />
            Archived Chats
          </h1>
          <div className="ml-auto text-sm text-gray-500 dark:text-gray-400">
            {filteredChats.length} chat{filteredChats.length !== 1 ? 's' : ''}
          </div>
        </div>

        {/* Search */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-800">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search archived chats..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Chat List */}
        <div className="flex-1 overflow-y-auto">
          {filteredChats.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <div className="mb-4">
                <Archive className="w-12 h-12 mx-auto text-gray-300" />
              </div>
              <p className="text-sm mb-4">
                {searchQuery 
                  ? `No archived chats found matching "${searchQuery}"` 
                  : 'No archived chats yet'
                }
              </p>
              {!searchQuery && (
                <p className="text-xs text-gray-400">
                  Chats you archive will appear here
                </p>
              )}
            </div>
          ) : (
            <div className="p-4 space-y-1">
              {filteredChats.map((chat) => (
                <ChatItem
                  key={chat.id}
                  chat={chat}
                  isActive={false}
                  isSelected={selectedChat === chat.id}
                  shiftKey={shiftKey}
                  onSelect={() => handleChatSelect(chat.id)}
                  onUnselect={handleChatUnselect}
                  onRename={(newTitle) => handleChatRename(chat.id, newTitle)}
                  onDelete={() => handleChatDelete(chat.id)}
                  onClone={() => handleChatClone(chat.id)}
                  onShare={() => handleChatShare(chat.id)}
                  onArchive={() => handleChatUnarchive(chat.id)} // This will unarchive
                  onPin={() => handleChatPin(chat.id)}
                  onGenerateTitle={() => handleGenerateTitle(chat.id)}
                  onChange={loadArchivedChats}
                  draggable={false}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </AppLayout>
  );
}