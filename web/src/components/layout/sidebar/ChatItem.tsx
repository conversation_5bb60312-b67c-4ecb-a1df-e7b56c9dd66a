'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tooltip } from '@/components/ui/tooltip';
import { ConfirmModal } from '@/components/ui/modal';
import { 
  MoreVertical, 
  Edit, 
  Copy, 
  Share, 
  Archive, 
  Trash2,
  MessageSquare,
  Pin,
  PinOff,
  Tag,
  Sparkles,
  Check,
  X
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface Chat {
  id: string;
  title: string;
  updated_at: string;
  created_at: string;
  pinned?: boolean;
  archived?: boolean;
  tags?: string[];
  folder_id?: string;
  type?: 'chat' | 'agent';
  chat?: {
    messages?: unknown[];
    models?: string[];
    type?: 'chat' | 'agent';
  };
}

interface ChatItemProps {
  chat: Chat;
  isActive?: boolean;
  isSelected?: boolean;
  shiftKey?: boolean;
  onSelect?: () => void;
  onUnselect?: () => void;
  onRename?: (newTitle: string) => void;
  onDelete?: () => void;
  onClone?: () => void;
  onShare?: () => void;
  onArchive?: () => void;
  onPin?: () => void;
  onTag?: (tag: string) => void;
  onGenerateTitle?: () => Promise<string | null>;
  onChange?: () => void;
  className?: string;
  draggable?: boolean;
  onDragStart?: (e: React.DragEvent) => void;
  onDrag?: (e: React.DragEvent) => void;
  onDragEnd?: (e: React.DragEvent) => void;
}

export const ChatItem: React.FC<ChatItemProps> = ({
  chat,
  isActive = false,
  isSelected = false,
  shiftKey = false,
  onSelect,
  onUnselect,
  onRename,
  onDelete,
  onClone,
  onShare,
  onArchive,
  onPin,
  onTag,
  onGenerateTitle,
  onChange,
  className,
  draggable = false,
  onDragStart,
  onDrag,
  onDragEnd
}) => {
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState(chat.title);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });
  const inputRef = useRef<HTMLInputElement>(null);
  const itemRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  // Handle global hover reset event
  useEffect(() => {
    const handleResetHover = () => {
      if (!isActive && !isSelected) {
        setIsHovered(false);
      }
    };

    document.addEventListener('resetChatItemHover', handleResetHover);
    return () => {
      document.removeEventListener('resetChatItemHover', handleResetHover);
    };
  }, [chat.id, isActive, isSelected]);

  const handleClick = () => {
    if (isEditing) return;
    
    // Determine the chat type - check nested chat.type first, then fallback to top-level type
    const chatType = chat.chat?.type || chat.type || 'chat';
    const navigationUrl = `/c/${chat.id}${chatType === 'agent' ? '?mode=agent' : ''}`;
    
    console.log('ChatItem clicked, navigating to:', navigationUrl, 'Type:', chatType); // Debug log
    
    // Reset hover state of other items
    document.dispatchEvent(new CustomEvent('resetChatItemHover'));
    
    router.push(navigationUrl);
    onSelect?.();
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsEditing(true);
  };

  const handleRename = async () => {
    if (editTitle.trim() === '') {
      // Handle empty title error
      setEditTitle(chat.title);
      setIsEditing(false);
      return;
    }
    
    if (editTitle.trim() !== chat.title) {
      await onRename?.(editTitle.trim());
      onChange?.();
    }
    setIsEditing(false);
  };

  const handleGenerateTitle = async () => {
    if (!onGenerateTitle) return;
    
    setIsGenerating(true);
    try {
      const generatedTitle = await onGenerateTitle();
      if (generatedTitle && generatedTitle !== chat.title) {
        setEditTitle(generatedTitle);
        await onRename?.(generatedTitle);
        onChange?.();
      }
    } catch (error) {
      console.error('Failed to generate title:', error);
    } finally {
      setIsGenerating(false);
      setIsEditing(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleRename();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      setEditTitle(chat.title);
      setIsEditing(false);
    }
  };

  const handleInputBlur = async (e: React.FocusEvent) => {
    // Check if focus moved to generate button
    if (e.relatedTarget?.id === `generate-title-button-${chat.id}`) {
      return;
    }
    await handleRename();
  };

  const handleMenuAction = (action: string) => {
    switch (action) {
      case 'rename':
        setEditTitle(chat.title);
        setIsEditing(true);
        break;
      case 'clone':
        onClone?.();
        onChange?.();
        break;
      case 'share':
        onShare?.();
        break;
      case 'archive':
        onArchive?.();
        onChange?.();
        break;
      case 'pin':
        onPin?.();
        onChange?.();
        break;
      case 'delete':
        setShowDeleteConfirm(true);
        break;
    }
  };

  // Drag handlers
  const handleDragStart = useCallback((e: React.DragEvent) => {
    e.stopPropagation();
    
    // Create invisible drag image
    const dragImage = new Image();
    dragImage.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
    e.dataTransfer.setDragImage(dragImage, 0, 0);
    
    // Set drag data
    e.dataTransfer.setData('text/plain', JSON.stringify({
      type: 'chat',
      id: chat.id,
      item: chat
    }));
    
    setIsDragging(true);
    if (itemRef.current) {
      itemRef.current.style.opacity = '0.5';
    }
    
    onDragStart?.(e);
  }, [chat, onDragStart]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.stopPropagation();
    setDragPosition({ x: e.clientX, y: e.clientY });
    onDrag?.(e);
  }, [onDrag]);

  const handleDragEnd = useCallback((e: React.DragEvent) => {
    e.stopPropagation();
    setIsDragging(false);
    if (itemRef.current) {
      itemRef.current.style.opacity = '1';
    }
    onDragEnd?.(e);
  }, [onDragEnd]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays}d`;
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  return (
    <>
      {/* Drag Ghost */}
      {isDragging && dragPosition.x && dragPosition.y && (
        <div 
          className="fixed pointer-events-none z-50 bg-black/80 backdrop-blur-2xl px-2 py-1 rounded-lg w-fit max-w-40"
          style={{
            left: dragPosition.x + 10,
            top: dragPosition.y - 10
          }}
        >
          <div className="flex items-center gap-1">
            <MessageSquare className="w-[18px] h-[18px] text-white" strokeWidth={2} />
            <div className="text-xs text-white truncate">
              {chat.title}
            </div>
          </div>
        </div>
      )}

      <div
        ref={itemRef}
        draggable={draggable && !isEditing}
        onDragStart={handleDragStart}
        onDrag={handleDrag}
        onDragEnd={handleDragEnd}
        className={cn(
          "group relative flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 cursor-pointer",
          isActive 
            ? "bg-blue-600 text-white" 
            : isSelected || isHovered
            ? "bg-blue-600 text-white"
            : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800",
          isGenerating && "cursor-not-allowed",
          className
        )}
        onClick={handleClick}
        onDoubleClick={handleDoubleClick}
        onMouseEnter={(e) => {
          setIsHovered(true);
          if (!(isActive || isEditing || isSelected)) {
            e.currentTarget.style.backgroundColor = '#0081FB';
            e.currentTarget.style.color = 'white';
          }
        }}
        onMouseLeave={(e) => {
          setIsHovered(false);
          if (!(isActive || isEditing || isSelected)) {
            e.currentTarget.style.backgroundColor = '';
            e.currentTarget.style.color = '';
          }
        }}
      >
        {/* Chat Icon */}
        <div className="flex-shrink-0">
          <MessageSquare className="w-4 h-4" />
        </div>

        {/* Chat Title */}
        <div className="flex-1 min-w-0">
          {isEditing ? (
            <div className="flex items-center gap-2">
              <Input
                ref={inputRef}
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                onBlur={handleInputBlur}
                onKeyDown={handleKeyPress}
                placeholder={isGenerating ? 'Generating...' : ''}
                disabled={isGenerating}
                className="h-6 px-1 py-0 text-sm bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 flex-1"
                onClick={(e) => e.stopPropagation()}
              />
              {onGenerateTitle && (
                <div className="absolute right-12 top-1/2 -translate-y-1/2 flex items-center gap-1">
                  <Tooltip content="Generate Title">
                    <Button
                      id={`generate-title-button-${chat.id}`}
                      size="sm"
                      variant="ghost"
                      disabled={isGenerating}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleGenerateTitle();
                      }}
                      className="h-5 w-5 p-0 hover:bg-white/20"
                    >
                      <Sparkles className="w-3 h-3" strokeWidth={2} />
                    </Button>
                  </Tooltip>
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col">
              <div className="text-sm font-medium truncate overflow-hidden w-[calc(100%-30px)] h-[20px]">
                {chat.title || 'New Chat'}
              </div>
              <div className={cn(
                "text-xs opacity-70",
                isActive || isSelected || isHovered ? "text-white" : "text-gray-500"
              )}>
                {formatDate(chat.updated_at)}
              </div>
            </div>
          )}
        </div>

        {/* Pinned Indicator */}
        {chat.pinned && (
          <div className="flex-shrink-0">
            <Pin className="w-3 h-3 opacity-70" />
          </div>
        )}

        {/* Tags */}
        {chat.tags && chat.tags.length > 0 && (
          <div className="flex-shrink-0">
            <Tag className="w-3 h-3 opacity-70" />
          </div>
        )}

        {/* Menu */}
        <div className={cn(
          "absolute right-1 top-1/2 -translate-y-1/2 py-1 pr-0.5 mr-1.5 pl-5 z-40",
          isActive || isEditing ? "" : isSelected ? "" : "invisible group-hover:visible"
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        >
          {isEditing ? (
            <div className="flex items-center space-x-1.5 z-10">
              {onGenerateTitle && (
                <Tooltip content="Generate">
                  <Button
                    id={`generate-title-button-${chat.id}`}
                    size="sm"
                    variant="ghost"
                    disabled={isGenerating}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleGenerateTitle();
                    }}
                    className="h-5 w-5 p-0 hover:bg-white/20"
                  >
                    <Sparkles className="w-3 h-3" strokeWidth={2} />
                  </Button>
                </Tooltip>
              )}
            </div>
          ) : (
            <div className="flex items-center z-50">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 text-current hover:bg-white/20"
                    onClick={(e) => {
                      e.stopPropagation();
                      onSelect?.();
                    }}
                  >
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48 z-[9999] bg-white">
                  <DropdownMenuItem onClick={() => handleMenuAction('rename')} className="hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer">
                    <Edit className="w-4 h-4 mr-2" />
                    Rename
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleMenuAction('clone')} className="hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer">
                    <Copy className="w-4 h-4 mr-2" />
                    Duplicate
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleMenuAction('share')} className="hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer">
                    <Share className="w-4 h-4 mr-2" />
                    Share
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => handleMenuAction('pin')} className="hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer">
                    {chat.pinned ? (
                      <>
                        <PinOff className="w-4 h-4 mr-2" />
                        Unpin
                      </>
                    ) : (
                      <>
                        <Pin className="w-4 h-4 mr-2" />
                        Pin
                      </>
                    )}
                  </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleMenuAction('archive')} className="hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer">
                    <Archive className="w-4 h-4 mr-2" />
                    {chat.archived ? 'Unarchive' : 'Archive'}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => handleMenuAction('delete')}
                    className="text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                  </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        show={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={() => {
          onDelete?.();
          onChange?.();
          setShowDeleteConfirm(false);
        }}
        title="Delete chat?"
        message={`This will delete "${chat.title}".`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
      />
    </>
  );
};
