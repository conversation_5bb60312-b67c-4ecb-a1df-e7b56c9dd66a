// Banner Types
export type Banner = {
  id: string;
  type: string;
  title?: string;
  content: string;
  url?: string;
  dismissible?: boolean;
  timestamp: number;
};

// TTS Types
export enum TTS_RESPONSE_SPLIT {
  PUNCTUATION = 'punctuation',
  PARAGRAPHS = 'paragraphs',
  NONE = 'none'
}

// User Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user';
  profile_image_url?: string;
  created_at: number;
  updated_at: number;
  last_active_at?: number;
  settings?: UserSettings;
  permissions?: UserPermissions;
}

export interface SessionUser extends User {
  token?: string;
}

export interface UserSettings {
  ui?: {
    theme?: string;
    language?: string;
    fontSize?: string;
    codeTheme?: string;
    showTimestamp?: boolean;
    showUsername?: boolean;
  };
  chat?: {
    models?: string[];
    defaultModel?: string;
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
  };
  notifications?: {
    enabled?: boolean;
    sounds?: boolean;
    desktop?: boolean;
  };
  directConnections?: {
    OPENAI_API_BASE_URLS?: string[];
    OPENAI_API_KEYS?: string[];
    OPENAI_API_CONFIGS?: Record<string, any>;
  };
}

export interface UserPermissions {
  features?: {
    notes?: boolean;
    workspace?: boolean;
    admin?: boolean;
  };
}

// Chat Types
export interface Chat {
  id: string;
  title: string;
  user_id: string;
  created_at: number;
  updated_at: number;
  archived?: boolean;
  pinned?: boolean;
  tags?: string[];
  models?: string[];
  messages?: Message[]; // For backward compatibility
  share_id?: string;
  type?: 'chat' | 'agent'; // Add type field to distinguish between chat and agent modes
  // Nested chat data structure (actual API response format)
  chat?: {
    title?: string;
    models?: string[];
    type?: 'chat' | 'agent'; // Also add to nested structure
    history?: {
      messages: Record<string, any>;
      currentId?: string;
    };
    messages?: Message[] | Record<string, any>; // Legacy format
  };
}

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  timestamp: number;
  model?: string;
  files?: FileAttachment[];
  metadata?: MessageMetadata;
  parentId?: string | null;
  childrenIds?: string[];
  done?: boolean;
  error?: boolean | string;
  sources?: Citation[];
  citations?: Citation[];
  web_search_results?: any[];
  code_executions?: any[];
}

export interface MessageMetadata {
  citations?: Citation[];
  tools?: ToolCall[];
  usage?: TokenUsage;
}

export interface Citation {
  source: string;
  url?: string;
  title?: string;
  content?: string;
}

export interface ToolCall {
  id: string;
  name: string;
  arguments: Record<string, any>;
  result?: any;
}

export interface TokenUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

// File Types
export interface FileAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url?: string;
  content?: string;
}

// Model Types
export interface Model {
  id: string;
  name: string;
  object: string;
  created: number;
  owned_by: string;
  info?: ModelInfo;
  capabilities?: ModelCapabilities;
}

export interface ModelInfo {
  base_model_id?: string;
  description?: string;
  license?: string;
  requires_auth?: boolean;
  size?: number;
  params?: Record<string, any>;
}

export interface ModelCapabilities {
  vision?: boolean;
  function_calling?: boolean;
  streaming?: boolean;
  max_tokens?: number;
}

// API Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  total: number;
  page: number;
  limit: number;
  has_more: boolean;
}

// Configuration Types
export interface Config {
  version?: string;
  features?: {
    auth?: boolean;
    auth_trusted_header?: boolean;
    enable_ldap?: boolean;
    enable_signup?: boolean;
    enable_notes?: boolean;
    enable_workspace?: boolean;
    enable_admin_export?: boolean;
    enable_direct_connections?: boolean;
  };
  ui?: {
    default_locale?: string;
    prompt_suggestions?: PromptSuggestion[];
  };
  oauth?: {
    providers?: OAuthProvider[];
  };
  onboarding?: boolean;
}

export interface PromptSuggestion {
  title: string;
  content: string;
}

export interface OAuthProvider {
  name: string;
  client_id: string;
  scope: string;
  redirect_uri: string;
}

// Store Types
export interface AppState {
  user: SessionUser | null;
  config: Config | null;
  models: Model[];
  chats: Chat[];
  currentChat: Chat | null;
  settings: UserSettings;
  theme: string;
  loading: boolean;
  error: string | null;
}

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Event Types
export interface ChatEvent {
  type: 'message' | 'typing' | 'error';
  data: any;
  timestamp: number;
}

// WebSocket Types
export interface WebSocketMessage {
  type: string;
  data: any;
  id?: string;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// Theme Types
export type ThemeMode = 'light' | 'dark' | 'system';

// Navigation Types
export interface NavItem {
  label: string;
  href: string;
  icon?: React.ComponentType;
  badge?: string | number;
  children?: NavItem[];
}

// Search Types
export interface SearchResult {
  id: string;
  type: 'chat' | 'message' | 'file' | 'user';
  title: string;
  content?: string;
  url?: string;
  metadata?: Record<string, any>;
}

// Notification Types
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message?: string;
  duration?: number;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary';
}
