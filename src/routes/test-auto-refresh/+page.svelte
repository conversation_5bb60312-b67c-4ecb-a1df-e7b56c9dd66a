<script lang="ts">
	import { onMount } from 'svelte';
	import { createNewChat, updateChatById, deleteChatById } from '$lib/apis/chats';
	import { toast } from 'svelte-sonner';

	let token = '';
	let chatTitle = '';
	let chatId = '';
	let isLoading = false;
	let logs: string[] = [];

	// 获取token
	onMount(() => {
		token = localStorage.getItem('token') || '';
		if (!token) {
			addLog('警告: 未找到token，请先登录');
		}

		// 监听聊天列表刷新事件
		const handleChatListRefresh = (event) => {
			const { data, page } = event.detail;
			addLog(`🎉 检测到聊天列表自动刷新事件! 页码: ${page}, 数据条数: ${data.length || 0}`);
		};

		window.addEventListener('chatListRefreshed', handleChatListRefresh);

		// 清理事件监听器
		return () => {
			window.removeEventListener('chatListRefreshed', handleChatListRefresh);
		};
	});

	function addLog(message: string) {
		const timestamp = new Date().toLocaleTimeString();
		logs = [`[${timestamp}] ${message}`, ...logs];
	}

	async function testCreateNewChat() {
		if (!token) {
			toast.error('请先登录获取token');
			return;
		}

		if (!chatTitle.trim()) {
			toast.error('请输入聊天标题');
			return;
		}

		isLoading = true;
		addLog('开始创建新聊天...');

		try {
			const newChat = await createNewChat(token, {
				title: chatTitle,
				messages: [],
				timestamp: Date.now()
			});

			if (newChat) {
				chatId = newChat.id;
				addLog(`✅ 聊天创建成功: ${newChat.id}`);
				addLog('🔄 应该已自动调用 /api/v1/chats/?page=1 刷新聊天列表');
				toast.success('聊天创建成功！请查看网络面板确认是否调用了刷新接口');
			}
		} catch (error) {
			addLog(`❌ 创建聊天失败: ${error}`);
			toast.error('创建聊天失败');
		} finally {
			isLoading = false;
		}
	}

	async function testUpdateChat() {
		if (!token) {
			toast.error('请先登录获取token');
			return;
		}

		if (!chatId.trim()) {
			toast.error('请先创建聊天或输入聊天ID');
			return;
		}

		isLoading = true;
		addLog('开始更新聊天...');

		try {
			const updatedChat = await updateChatById(token, chatId, {
				title: `${chatTitle} (已更新)`,
				updated_at: Date.now()
			});

			if (updatedChat) {
				addLog(`✅ 聊天更新成功: ${chatId}`);
				addLog('🔄 应该已自动调用 /api/v1/chats/?page=1 刷新聊天列表');
				toast.success('聊天更新成功！请查看网络面板确认是否调用了刷新接口');
			}
		} catch (error) {
			addLog(`❌ 更新聊天失败: ${error}`);
			toast.error('更新聊天失败');
		} finally {
			isLoading = false;
		}
	}

	async function testDeleteChat() {
		if (!token) {
			toast.error('请先登录获取token');
			return;
		}

		if (!chatId.trim()) {
			toast.error('请先创建聊天或输入聊天ID');
			return;
		}

		if (!confirm('确定要删除这个聊天吗？')) {
			return;
		}

		isLoading = true;
		addLog('开始删除聊天...');

		try {
			await deleteChatById(token, chatId);
			addLog(`✅ 聊天删除成功: ${chatId}`);
			addLog('🔄 应该已自动调用 /api/v1/chats/?page=1 刷新聊天列表');
			toast.success('聊天删除成功！请查看网络面板确认是否调用了刷新接口');
			
			// 清空聊天ID
			chatId = '';
		} catch (error) {
			addLog(`❌ 删除聊天失败: ${error}`);
			toast.error('删除聊天失败');
		} finally {
			isLoading = false;
		}
	}

	function clearLogs() {
		logs = [];
	}
</script>

<svelte:head>
	<title>测试自动刷新功能</title>
</svelte:head>

<div class="container">
	<h1>测试聊天自动刷新功能</h1>
	
	<div class="info-box">
		<h3>📋 测试说明</h3>
		<p>这个页面用于测试聊天API调用后是否会自动调用 <code>/api/v1/chats/?page=1</code> 接口。</p>
		<p>请打开浏览器的开发者工具 → Network 面板，然后执行下面的操作，观察是否有对应的网络请求。</p>
	</div>

	<div class="form-section">
		<div class="input-group">
			<label for="token">Token (自动从localStorage获取):</label>
			<input 
				id="token"
				type="text" 
				bind:value={token} 
				placeholder="用户token"
				disabled={isLoading}
			/>
		</div>

		<div class="input-group">
			<label for="title">聊天标题:</label>
			<input 
				id="title"
				type="text" 
				bind:value={chatTitle} 
				placeholder="输入聊天标题"
				disabled={isLoading}
			/>
		</div>

		<div class="input-group">
			<label for="chatId">聊天ID (创建后自动填入):</label>
			<input 
				id="chatId"
				type="text" 
				bind:value={chatId} 
				placeholder="聊天ID"
				disabled={isLoading}
			/>
		</div>
	</div>

	<div class="button-section">
		<button 
			on:click={testCreateNewChat} 
			disabled={isLoading || !token || !chatTitle.trim()}
			class="btn btn-primary"
		>
			{isLoading ? '创建中...' : '🆕 创建新聊天'}
		</button>

		<button 
			on:click={testUpdateChat} 
			disabled={isLoading || !token || !chatId.trim()}
			class="btn btn-secondary"
		>
			{isLoading ? '更新中...' : '✏️ 更新聊天'}
		</button>

		<button 
			on:click={testDeleteChat} 
			disabled={isLoading || !token || !chatId.trim()}
			class="btn btn-danger"
		>
			{isLoading ? '删除中...' : '🗑️ 删除聊天'}
		</button>

		<button 
			on:click={clearLogs} 
			disabled={isLoading}
			class="btn btn-info"
		>
			🧹 清空日志
		</button>
	</div>

	<div class="logs-section">
		<h3>📝 操作日志</h3>
		<div class="logs-container">
			{#each logs as log}
				<div class="log-item">{log}</div>
			{/each}
			{#if logs.length === 0}
				<div class="log-item empty">暂无日志</div>
			{/if}
		</div>
	</div>

	<div class="instructions">
		<h3>🔍 如何验证自动刷新功能</h3>
		<ol>
			<li>打开浏览器开发者工具 (F12)</li>
			<li>切换到 "Network" (网络) 面板</li>
			<li>在过滤器中输入 "chats" 来只显示聊天相关的请求</li>
			<li>执行上面的操作 (创建/更新/删除聊天)</li>
			<li>观察是否有对 <code>/api/v1/chats/?page=1</code> 的GET请求</li>
			<li>如果看到这个请求，说明自动刷新功能正常工作</li>
		</ol>
	</div>
</div>

<style>
	.container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 20px;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}

	h1 {
		color: #333;
		text-align: center;
		margin-bottom: 30px;
	}

	.info-box {
		background: #e3f2fd;
		border: 1px solid #2196f3;
		border-radius: 8px;
		padding: 15px;
		margin-bottom: 20px;
	}

	.info-box h3 {
		margin-top: 0;
		color: #1976d2;
	}

	.info-box code {
		background: #fff;
		padding: 2px 6px;
		border-radius: 4px;
		font-family: 'Courier New', monospace;
	}

	.form-section {
		background: #f5f5f5;
		padding: 20px;
		border-radius: 8px;
		margin-bottom: 20px;
	}

	.input-group {
		margin-bottom: 15px;
	}

	.input-group label {
		display: block;
		margin-bottom: 5px;
		font-weight: bold;
		color: #555;
	}

	.input-group input {
		width: 100%;
		padding: 10px;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 14px;
		box-sizing: border-box;
	}

	.input-group input:disabled {
		background-color: #f0f0f0;
		cursor: not-allowed;
	}

	.button-section {
		display: flex;
		flex-wrap: wrap;
		gap: 10px;
		margin-bottom: 30px;
	}

	.btn {
		padding: 12px 20px;
		border: none;
		border-radius: 6px;
		cursor: pointer;
		font-size: 14px;
		font-weight: bold;
		transition: all 0.2s;
		min-width: 140px;
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.btn-primary { background: #2196f3; color: white; }
	.btn-secondary { background: #757575; color: white; }
	.btn-danger { background: #f44336; color: white; }
	.btn-info { background: #00bcd4; color: white; }

	.btn:hover:not(:disabled) {
		transform: translateY(-1px);
		box-shadow: 0 2px 8px rgba(0,0,0,0.2);
	}

	.logs-section {
		background: #fff;
		border: 1px solid #ddd;
		border-radius: 8px;
		padding: 20px;
		margin-bottom: 20px;
	}

	.logs-section h3 {
		margin-top: 0;
		color: #333;
	}

	.logs-container {
		max-height: 300px;
		overflow-y: auto;
		background: #f8f8f8;
		border: 1px solid #e0e0e0;
		border-radius: 4px;
		padding: 10px;
	}

	.log-item {
		padding: 5px 0;
		border-bottom: 1px solid #eee;
		font-family: 'Courier New', monospace;
		font-size: 13px;
		line-height: 1.4;
	}

	.log-item:last-child {
		border-bottom: none;
	}

	.log-item.empty {
		color: #999;
		font-style: italic;
		text-align: center;
	}

	.instructions {
		background: #fff3e0;
		border: 1px solid #ff9800;
		border-radius: 8px;
		padding: 20px;
	}

	.instructions h3 {
		margin-top: 0;
		color: #f57c00;
	}

	.instructions ol {
		margin: 0;
		padding-left: 20px;
	}

	.instructions li {
		margin-bottom: 8px;
		line-height: 1.5;
	}

	.instructions code {
		background: #fff;
		padding: 2px 6px;
		border-radius: 4px;
		font-family: 'Courier New', monospace;
	}
</style>
