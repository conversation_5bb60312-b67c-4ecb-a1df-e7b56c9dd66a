import { 
	createNewChat, 
	updateChatById, 
	deleteChatById,
	importChat,
	addTagById,
	deleteTagById
} from './index';
import { createAutoRefreshApi, withAutoRefresh } from '$lib/utils/chatRefresh';

/**
 * 自动刷新版本的聊天API函数
 * 这些函数在执行完成后会自动调用 /api/v1/chats/?page=1 刷新聊天列表
 */

// 创建新聊天（自动刷新版本）
export const createNewChatAuto = createAutoRefreshApi(createNewChat, {
	refreshChatList: true,
	refreshPage: 1,
	tokenIndex: 0 // token是第一个参数
});

// 更新聊天（自动刷新版本）
export const updateChatByIdAuto = createAutoRefreshApi(updateChatById, {
	refreshChatList: true,
	refreshPage: 1,
	tokenIndex: 0 // token是第一个参数
});

// 删除聊天（自动刷新版本）
export const deleteChatByIdAuto = createAutoRefreshApi(deleteChatById, {
	refreshChatList: true,
	refreshPage: 1,
	tokenIndex: 0 // token是第一个参数
});

// 导入聊天（自动刷新版本）
export const importChatAuto = createAutoRefreshApi(importChat, {
	refreshChatList: true,
	refreshPage: 1,
	tokenIndex: 0 // token是第一个参数
});

// 添加标签（自动刷新版本）
export const addTagByIdAuto = createAutoRefreshApi(addTagById, {
	refreshChatList: true,
	refreshPage: 1,
	tokenIndex: 0 // token是第一个参数
});

// 删除标签（自动刷新版本）
export const deleteTagByIdAuto = createAutoRefreshApi(deleteTagById, {
	refreshChatList: true,
	refreshPage: 1,
	tokenIndex: 0 // token是第一个参数
});

/**
 * 手动触发聊天列表刷新的函数
 * @param token 用户token
 * @param page 页码，默认为1
 */
export async function refreshChatListManually(token: string, page: number = 1) {
	return withAutoRefresh(
		async () => {
			// 这里可以是任何需要执行的逻辑
			return { success: true, message: '手动刷新完成' };
		},
		token,
		{
			refreshChatList: true,
			refreshPage: page
		}
	);
}

/**
 * 批量操作后刷新聊天列表
 * @param operations 要执行的操作数组
 * @param token 用户token
 * @param refreshPage 刷新页码
 */
export async function batchOperationsWithRefresh<T>(
	operations: (() => Promise<T>)[],
	token: string,
	refreshPage: number = 1
): Promise<T[]> {
	// 执行所有操作
	const results = await Promise.all(operations);
	
	// 操作完成后刷新聊天列表
	await withAutoRefresh(
		async () => ({ success: true }),
		token,
		{
			refreshChatList: true,
			refreshPage: refreshPage
		}
	);
	
	return results;
}

/**
 * 使用示例：
 * 
 * // 1. 使用自动刷新版本的API
 * const newChat = await createNewChatAuto(token, chatData);
 * 
 * // 2. 手动触发刷新
 * await refreshChatListManually(token, 1);
 * 
 * // 3. 批量操作后刷新
 * await batchOperationsWithRefresh([
 *   () => updateChatById(token, chatId1, data1),
 *   () => updateChatById(token, chatId2, data2),
 * ], token, 1);
 * 
 * // 4. 自定义刷新逻辑
 * const result = await withAutoRefresh(
 *   () => someCustomApiCall(),
 *   token,
 *   {
 *     refreshChatList: true,
 *     refreshPage: 1,
 *     onSuccess: (result) => console.log('操作成功:', result),
 *     onError: (error) => console.error('操作失败:', error)
 *   }
 * );
 */
