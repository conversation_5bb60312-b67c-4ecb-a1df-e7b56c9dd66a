// 测试自动刷新功能的脚本
// 在浏览器控制台中运行此脚本

console.log('🚀 开始测试聊天自动刷新功能...');

// 获取token
const token = localStorage.getItem('token');
if (!token) {
    console.error('❌ 未找到token，请先登录');
} else {
    console.log('✅ 找到token:', token.substring(0, 20) + '...');
}

// 测试创建新聊天
async function testCreateNewChat() {
    console.log('\n📝 测试创建新聊天...');
    
    try {
        // 监听网络请求
        const originalFetch = window.fetch;
        let refreshCalled = false;
        
        window.fetch = function(...args) {
            const url = args[0];
            if (typeof url === 'string' && url.includes('/api/v1/chats/?page=1')) {
                console.log('🔄 检测到自动刷新请求:', url);
                refreshCalled = true;
            }
            return originalFetch.apply(this, args);
        };
        
        // 动态导入API函数
        const { createNewChat } = await import('./src/lib/apis/chats/index.ts');
        
        const testChat = {
            title: `测试聊天 ${new Date().toLocaleTimeString()}`,
            messages: [],
            timestamp: Date.now()
        };
        
        console.log('发送创建聊天请求...');
        const result = await createNewChat(token, testChat);
        
        // 恢复原始fetch
        window.fetch = originalFetch;
        
        if (result) {
            console.log('✅ 聊天创建成功:', result.id);
            
            // 等待一下看是否调用了刷新
            setTimeout(() => {
                if (refreshCalled) {
                    console.log('🎉 自动刷新功能正常工作！');
                } else {
                    console.log('❌ 未检测到自动刷新请求');
                }
            }, 1000);
            
            return result.id;
        } else {
            console.log('❌ 聊天创建失败');
        }
    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
}

// 测试更新聊天
async function testUpdateChat(chatId) {
    if (!chatId) {
        console.log('⚠️ 需要先创建聊天才能测试更新');
        return;
    }
    
    console.log('\n✏️ 测试更新聊天...');
    
    try {
        // 监听网络请求
        const originalFetch = window.fetch;
        let refreshCalled = false;
        
        window.fetch = function(...args) {
            const url = args[0];
            if (typeof url === 'string' && url.includes('/api/v1/chats/?page=1')) {
                console.log('🔄 检测到自动刷新请求:', url);
                refreshCalled = true;
            }
            return originalFetch.apply(this, args);
        };
        
        // 动态导入API函数
        const { updateChatById } = await import('./src/lib/apis/chats/index.ts');
        
        const updateData = {
            title: `更新的聊天 ${new Date().toLocaleTimeString()}`,
            updated_at: Date.now()
        };
        
        console.log('发送更新聊天请求...');
        const result = await updateChatById(token, chatId, updateData);
        
        // 恢复原始fetch
        window.fetch = originalFetch;
        
        if (result) {
            console.log('✅ 聊天更新成功');
            
            // 等待一下看是否调用了刷新
            setTimeout(() => {
                if (refreshCalled) {
                    console.log('🎉 自动刷新功能正常工作！');
                } else {
                    console.log('❌ 未检测到自动刷新请求');
                }
            }, 1000);
        } else {
            console.log('❌ 聊天更新失败');
        }
    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
}

// 运行完整测试
async function runFullTest() {
    console.log('🧪 运行完整测试流程...');
    
    // 测试创建
    const chatId = await testCreateNewChat();
    
    if (chatId) {
        // 等待2秒后测试更新
        setTimeout(async () => {
            await testUpdateChat(chatId);
        }, 2000);
    }
}

// 提供给用户的测试函数
window.testAutoRefresh = {
    createChat: testCreateNewChat,
    updateChat: testUpdateChat,
    fullTest: runFullTest
};

console.log(`
📋 测试说明:
1. 打开开发者工具的 Network 面板
2. 在过滤器中输入 "chats" 
3. 运行以下命令之一:
   - testAutoRefresh.createChat() // 测试创建聊天
   - testAutoRefresh.updateChat('聊天ID') // 测试更新聊天
   - testAutoRefresh.fullTest() // 运行完整测试

4. 观察是否有对 /api/v1/chats/?page=1 的GET请求
`);

// 自动运行测试（可选）
// runFullTest();
