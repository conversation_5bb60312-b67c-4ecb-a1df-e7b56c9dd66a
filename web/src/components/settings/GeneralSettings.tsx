'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Copy, ExternalLink } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { useSettingsStore } from '@/lib/stores';

interface GeneralSettingsProps {
  saveSettings: (settings: any) => void;
  onSave?: () => void;
}

export const GeneralSettings: React.FC<GeneralSettingsProps> = ({
  saveSettings,
  onSave
}) => {
  const { settings } = useSettingsStore();
  const [language, setLanguage] = useState((settings as any)?.language || 'en');
  const [copyFeedback, setCopyFeedback] = useState(false);
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update local state when settings change
  useEffect(() => {
    setLanguage((settings as any)?.language || 'en');
  }, [settings]);

  const handleLanguageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newLang = e.target.value;
    setLanguage(newLang);
    // Don't save immediately on change, wait for explicit save button click
  };

  const copyEmail = async () => {
    try {
      await navigator.clipboard.writeText('<EMAIL>');
      setCopyFeedback(true);
      setTimeout(() => setCopyFeedback(false), 2000);
    } catch (error) {
      console.error('Failed to copy email:', error);
    }
  };

  const saveHandler = async (): Promise<boolean> => {
    // Clear existing timeout
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    return new Promise((resolve) => {
      // 300ms debounce delay to match ifm-chat-feature-k2
      saveTimeoutRef.current = setTimeout(async () => {
        try {
          console.log('GeneralSettings saveHandler: Starting save with language:', language);
          // Save settings
          await saveSettings({ language });
          console.log('GeneralSettings saveHandler: Save completed successfully');
          resolve(true);
        } catch (error) {
          console.error('GeneralSettings saveHandler: Failed to save settings:', error);
          const errorMessage = error instanceof Error ? error.message : 'Failed to save settings';
          toast.error(`Failed to save settings: ${errorMessage}`);
          resolve(false);
        }
      }, 300);
    });
  };

  return (
    <div className="space-y-0">
      {/* Language Selection */}
      <div className="flex w-full justify-between py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="self-center text-sm font-medium text-gray-900 dark:text-white">
          Language
        </div>
        <div className="flex items-center">
          <select
            value={language}
            onChange={handleLanguageChange}
            className="px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="en">English</option>
            <option value="zh">中文 (Chinese)</option>
            <option value="ar">العربية (Arabic)</option>
            <option value="es">Español (Spanish)</option>
            <option value="fr">Français (French)</option>
            <option value="de">Deutsch (German)</option>
            <option value="ja">日本語 (Japanese)</option>
            <option value="ko">한국어 (Korean)</option>
            <option value="pt">Português (Portuguese)</option>
            <option value="ru">Русский (Russian)</option>
            <option value="hi">हिन्दी (Hindi)</option>
            <option value="it">Italiano (Italian)</option>
            <option value="nl">Nederlands (Dutch)</option>
            <option value="sv">Svenska (Swedish)</option>
            <option value="tr">Türkçe (Turkish)</option>
          </select>
        </div>
      </div>

      {/* Contact Us */}
      <div className="py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="py-0.5 flex w-full justify-between">
          <div className="self-center text-sm font-medium text-gray-900 dark:text-white">
            Contact Us
          </div>
          <button
            onClick={copyEmail}
            className={cn(
              "flex items-center gap-2 px-3 py-1.5 text-sm rounded-md transition-all",
              "text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20",
              "border border-transparent hover:border-blue-200 dark:hover:border-blue-800"
            )}
          >
            <span><EMAIL></span>
            <Copy className="w-3 h-3" />
            {copyFeedback && (
              <span className="text-xs text-green-600 dark:text-green-400 ml-1">
                Copied!
              </span>
            )}
          </button>
        </div>
      </div>

      {/* About IFM */}
      <div className="py-4 border-b border-gray-200 dark:border-gray-700">
        <a
          href="https://ifm.mbzuai.ac.ae/"
          target="_blank"
          rel="noopener noreferrer"
          className={cn(
            "flex items-center justify-between w-full py-0.5 text-sm font-medium transition-all",
            "text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
          )}
        >
          <span>About IFM</span>
          <ExternalLink className="w-4 h-4" />
        </a>
      </div>

      {/* Save Button */}
      <div className="flex justify-end pt-3 text-sm font-medium">
        <button
          onClick={async () => {
            const success = await saveHandler();
            if (success) {
              onSave?.(); // This will trigger the parent to show success toast and close modal
            }
          }}
          className="px-3.5 py-1.5 text-sm font-medium text-white transition rounded-full hover:opacity-90"
          style={{ backgroundColor: '#0081FB' }}
        >
          Save
        </button>
      </div>
    </div>
  );
};
