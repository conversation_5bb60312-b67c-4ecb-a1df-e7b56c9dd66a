import { WEBUI_API_BASE_URL } from '@/lib/constants';
import type { User, UserSettings } from '@/lib/types';

// Get user settings
export const getUserSettings = async (token: string): Promise<UserSettings> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/user/settings`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get user settings:', error);
    throw error;
  }
};

// Update user settings
export const updateUserSettings = async (
  token: string,
  settings: any
): Promise<any> => {
  try {
    console.log('updateUserSettings called with:', settings);
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/user/settings/update`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify(settings)
    });

    console.log('updateUserSettings response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json();
      console.error('updateUserSettings error response:', errorData);
      throw errorData;
    }

    const result = await response.json();
    console.log('updateUserSettings success response:', result);
    return result;
  } catch (error) {
    console.error('Failed to update user settings:', error);
    throw error;
  }
};

// Get user by ID
export const getUserById = async (token: string, userId: string): Promise<User> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/${userId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get user by ID:', error);
    throw error;
  }
};

// Get all users (admin only)
export const getUsers = async (token: string): Promise<User[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get users:', error);
    throw error;
  }
};

// Update user by ID (admin only)
export const updateUserById = async (
  token: string,
  userId: string,
  userData: Partial<User>
): Promise<User> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/${userId}/update`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify(userData)
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update user:', error);
    throw error;
  }
};

// Delete user by ID (admin only)
export const deleteUserById = async (token: string, userId: string): Promise<void> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/${userId}`, {
      method: 'DELETE',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to delete user:', error);
    throw error;
  }
};

// Get user permissions
export const getUserPermissions = async (token: string, userId: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/${userId}/permissions`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get user permissions:', error);
    throw error;
  }
};

// Update user permissions (admin only)
export const updateUserPermissions = async (
  token: string,
  userId: string,
  permissions: any
): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/${userId}/permissions`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify(permissions)
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update user permissions:', error);
    throw error;
  }
};

// Get user location and update
export const getAndUpdateUserLocation = async (token: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/user/location`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get and update user location:', error);
    throw error;
  }
};

// Export user data
export const exportUserData = async (token: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/user/export`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to export user data:', error);
    throw error;
  }
};

// Delete user account
export const deleteUserAccount = async (token: string): Promise<void> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/user/delete`, {
      method: 'DELETE',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to delete user account:', error);
    throw error;
  }
};

// Get user info by session user
export const getUserInfo = async (token: string): Promise<User> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/user/info`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get user info:', error);
    throw error;
  }
};

// Update user info
export const updateUserInfo = async (
  token: string,
  userInfo: Partial<User>
): Promise<User> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/user/info/update`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify(userInfo)
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update user info:', error);
    throw error;
  }
};
