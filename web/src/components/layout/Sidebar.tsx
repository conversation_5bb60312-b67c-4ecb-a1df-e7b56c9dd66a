'use client';

import React from 'react';
import { useUIStore, useAuthStore } from '@/lib/stores';
import { Button } from '@/components/ui/button';
import { ConversationList } from '@/components/ConversationList';
import { UserMenu } from '@/components/layout/sidebar/UserMenu';
import { cn } from '@/lib/utils';
import { PanelLeft, Plus, MessageSquare } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useChats } from '@/hooks/useChats';
import { useRouter } from 'next/navigation';

interface SidebarProps {
  className?: string;
  currentConversationId?: string;
  onConversationSelect?: (id: string) => void;
  onNewConversation?: (type?: 'chat' | 'agent') => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ 
  className, 
  currentConversationId,
  onConversationSelect = () => {},
  onNewConversation = () => {}
}) => {
  const { sidebarOpen, setSidebarOpen } = useUIStore();
  const { user } = useAuthStore();
  const { chats, isLoading, isInitialized, createChat, loadChats } = useChats();
  const router = useRouter();

  const [search, setSearch] = useState('');
  const [filteredChats, setFilteredChats] = useState(chats);

  // Load chats when user changes or sidebar opens (only if not initialized)
  useEffect(() => {
    if (user && sidebarOpen && !isInitialized && !isLoading) {
      loadChats();
    }
  }, [user, sidebarOpen, isInitialized, isLoading]); // Use isInitialized instead of chats.length

  // Filter chats based on search
  useEffect(() => {
    if (search.trim()) {
      const filtered = chats.filter(chat =>
        chat.title?.toLowerCase().includes(search.toLowerCase()) ||
        chat.messages?.some(msg =>
          msg.content?.toLowerCase().includes(search.toLowerCase())
        )
      );
      setFilteredChats(filtered);
    } else {
      setFilteredChats(chats);
    }
  }, [search, chats]);

  const handleNewChat = async () => {
    try {
      // Navigate to home page for new chat
      router.push('/');
      // Also trigger the parent's new conversation handler
      onNewConversation('chat');
    } catch (error) {
      console.error('Failed to start new chat:', error);
    }
  };

  const handleChatClick = (chatId: string) => {
    // Navigate to chat (will be implemented with router)
    console.log('Navigate to chat:', chatId);
  };

  if (!sidebarOpen) {
    return null;
  }

  return (
    <div className={cn(
      "fixed inset-y-0 left-0 z-50 w-80 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 transform transition-transform duration-300 ease-in-out",
      sidebarOpen ? "translate-x-0" : "-translate-x-full",
      className
    )}>
      <div className="flex flex-col h-full">
        {/* Header with toggle and new chat */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-800">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarOpen(false)}
              className="h-8 w-8 p-1 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              title="Close sidebar"
            >
              <PanelLeft className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            </Button>
            <button
              onClick={handleNewChat}
              className="text-lg font-medium text-gray-800 dark:text-white no-underline hover:text-gray-600 dark:hover:text-gray-300 transition-colors cursor-pointer"
              title="Start a new conversation"
            >
              New Chat
            </button>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleNewChat}
              className="h-8 w-8 p-1 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              title="New Chat"
            >
              <Plus className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            </Button>
          </div>
        </div>

        {/* Conversation List */}
        <div className="flex-1 overflow-hidden">
          <ConversationList
            currentConversationId={currentConversationId}
            onConversationSelect={onConversationSelect}
            onNewConversation={onNewConversation}
          />
        </div>

        {/* User Menu */}
        <div className="border-t border-gray-200 dark:border-gray-800">
          <UserMenu />
        </div>
      </div>
    </div>
  );
};
